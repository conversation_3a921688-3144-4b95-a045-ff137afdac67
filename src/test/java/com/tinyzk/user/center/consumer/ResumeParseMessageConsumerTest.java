package com.tinyzk.user.center.consumer;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import com.tinyzk.user.center.mapper.*;
import com.tinyzk.user.center.service.*;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ResumeParseMessageConsumer 测试类
 */
@ExtendWith(MockitoExtension.class)
class ResumeParseMessageConsumerTest {

    @Mock
    private ThirdPartyResumeParseService parseService;
    
    @Mock
    private OSSFileStorageService ossService;
    
    @Mock
    private ResumeDataConversionService conversionService;
    
    @Mock
    private ResumeParseRecordsMapper parseRecordsMapper;
    
    @Mock
    private BatchDatabaseService batchDatabaseService;
    
    @Mock
    private UserProfileMapper userProfileMapper;
    
    @Mock
    private UserContactMethodsMapper userContactMethodsMapper;
    
    @Mock
    private UserEducationHistoryMapper userEducationHistoryMapper;
    
    @Mock
    private UserWorkHistoryMapper userWorkHistoryMapper;
    
    @Mock
    private UserProjectHistoryMapper userProjectHistoryMapper;
    
    @Mock
    private UserSkillsMapper userSkillsMapper;
    
    @Mock
    private UserTrainingMapper userTrainingMapper;
    
    @Mock
    private ThreadPoolTaskExecutor executor;

    private MeterRegistry meterRegistry;
    private ResumeParseMessageConsumer consumer;

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        consumer = new ResumeParseMessageConsumer(
                parseService, ossService, conversionService, parseRecordsMapper,
                batchDatabaseService, userProfileMapper, userContactMethodsMapper,
                userEducationHistoryMapper, userWorkHistoryMapper, userProjectHistoryMapper,
                userSkillsMapper, userTrainingMapper, meterRegistry, executor
        );
    }

    @Test
    void testSaveConvertedData_Success() throws Exception {
        // 准备测试数据
        Long userId = 1L;
        ResumeParseMessage message = createTestMessage();
        ThirdPartyParseResultDTO parseResult = createTestParseResult();

        // Mock 转换服务返回值
        UserProfile userProfile = new UserProfile();
        userProfile.setUserId(userId);
        userProfile.setNickname("测试用户");
        when(conversionService.convertBasicInfo(eq(userId), any())).thenReturn(userProfile);

        List<UserContactMethods> contactMethods = new ArrayList<>();
        UserContactMethods contact = new UserContactMethods();
        contact.setUserId(userId);
        contact.setContactType(1);
        contact.setContactValue("<EMAIL>");
        contactMethods.add(contact);
        when(conversionService.convertContactInfo(eq(userId), any())).thenReturn(contactMethods);

        List<UserEducationHistory> educationHistory = new ArrayList<>();
        UserEducationHistory education = new UserEducationHistory();
        education.setUserId(userId);
        education.setSchoolName("测试大学");
        education.setMajor("计算机科学");
        educationHistory.add(education);
        when(conversionService.convertEducationExperience(eq(userId), any())).thenReturn(educationHistory);

        List<UserWorkHistory> workHistory = new ArrayList<>();
        UserWorkHistory work = new UserWorkHistory();
        work.setUserId(userId);
        work.setCompanyName("测试公司");
        work.setPositionName("软件工程师");
        workHistory.add(work);
        when(conversionService.convertWorkExperience(eq(userId), any())).thenReturn(workHistory);

        List<UserProjectHistory> projectHistory = new ArrayList<>();
        UserProjectHistory project = new UserProjectHistory();
        project.setUserId(userId);
        project.setProjectName("测试项目");
        projectHistory.add(project);
        when(conversionService.convertProjectExperience(eq(userId), any())).thenReturn(projectHistory);

        List<UserTraining> trainingHistory = new ArrayList<>();
        UserTraining training = new UserTraining();
        training.setUserId(userId);
        training.setTrainingName("Java高级培训");
        training.setTrainingProvider("培训机构");
        trainingHistory.add(training);
        when(conversionService.convertTrainingExperience(eq(userId), any())).thenReturn(trainingHistory);

        List<UserSkills> skills = new ArrayList<>();
        UserSkills skill = new UserSkills();
        skill.setUserId(userId);
        skill.setSkillName("Java");
        skills.add(skill);
        when(conversionService.convertSkills(eq(userId), any())).thenReturn(skills);

        // Mock Mapper 返回值
        when(userProfileMapper.selectByUserId(userId)).thenReturn(null);
        when(userProfileMapper.insert(any(UserProfile.class))).thenReturn(1);
        
        when(userContactMethodsMapper.selectByUserIdAndTypeAndValue(anyLong(), anyInt(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(contactMethods), eq(userContactMethodsMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));
        
        when(userEducationHistoryMapper.selectByUserIdAndSchoolAndMajor(anyLong(), anyString(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(educationHistory), eq(userEducationHistoryMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));
        
        when(userWorkHistoryMapper.selectByUserIdAndCompanyAndPosition(anyLong(), anyString(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(workHistory), eq(userWorkHistoryMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));
        
        when(userProjectHistoryMapper.selectByUserIdAndProjectName(anyLong(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(projectHistory), eq(userProjectHistoryMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));

        when(userTrainingMapper.selectByUserIdAndNameAndProvider(anyLong(), anyString(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(trainingHistory), eq(userTrainingMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));

        when(userSkillsMapper.selectByUserIdAndSkillName(anyLong(), anyString())).thenReturn(null);
        when(batchDatabaseService.batchInsert(eq(skills), eq(userSkillsMapper)))
                .thenReturn(CompletableFuture.completedFuture(1));

        // 使用反射调用私有方法进行测试
        java.lang.reflect.Method method = ResumeParseMessageConsumer.class
                .getDeclaredMethod("saveConvertedData", Long.class, ThirdPartyParseResultDTO.class, ResumeParseMessage.class);
        method.setAccessible(true);
        
        // 执行测试
        method.invoke(consumer, userId, parseResult, message);

        // 验证调用
        verify(conversionService).convertBasicInfo(eq(userId), any());
        verify(conversionService).convertContactInfo(eq(userId), any());
        verify(conversionService).convertEducationExperience(eq(userId), any());
        verify(conversionService).convertWorkExperience(eq(userId), any());
        verify(conversionService).convertProjectExperience(eq(userId), any());
        verify(conversionService).convertTrainingExperience(eq(userId), any());
        verify(conversionService).convertSkills(eq(userId), any());

        verify(userProfileMapper).insert(any(UserProfile.class));
        verify(batchDatabaseService, times(6)).batchInsert(any(), any());
    }

    private ResumeParseMessage createTestMessage() {
        ResumeParseMessage message = new ResumeParseMessage();
        message.setMessageId("test-message-id");
        message.setUserId(1L);
        message.setFileName("test-resume.pdf");
        message.setFileType("pdf");
        message.setFileSize(1024L);
        message.setOssKey("test/resume.pdf");
        return message;
    }

    private ThirdPartyParseResultDTO createTestParseResult() {
        ThirdPartyParseResultDTO result = new ThirdPartyParseResultDTO();
        result.setErrorCode(0);
        result.setErrorMessage(null);
        
        ThirdPartyParseResultDTO.ParsingResult parsingResult = new ThirdPartyParseResultDTO.ParsingResult();
        
        // 基本信息
        ThirdPartyParseResultDTO.BasicInfo basicInfo = new ThirdPartyParseResultDTO.BasicInfo();
        basicInfo.setName("测试用户");
        basicInfo.setGender("男");
        parsingResult.setBasicInfo(basicInfo);
        
        // 联系方式
        ThirdPartyParseResultDTO.ContactInfo contactInfo = new ThirdPartyParseResultDTO.ContactInfo();
        contactInfo.setEmail("<EMAIL>");
        contactInfo.setPhoneNumber("13800138000");
        parsingResult.setContactInfo(contactInfo);
        
        // 教育经历
        List<ThirdPartyParseResultDTO.EducationExperience> educationList = new ArrayList<>();
        ThirdPartyParseResultDTO.EducationExperience education = new ThirdPartyParseResultDTO.EducationExperience();
        education.setSchoolName("测试大学");
        education.setMajor("计算机科学");
        education.setDegree("本科");
        educationList.add(education);
        parsingResult.setEducationExperience(educationList);
        
        // 工作经历
        List<ThirdPartyParseResultDTO.WorkExperience> workList = new ArrayList<>();
        ThirdPartyParseResultDTO.WorkExperience work = new ThirdPartyParseResultDTO.WorkExperience();
        work.setCompanyName("测试公司");
        work.setJobTitle("软件工程师");
        workList.add(work);
        parsingResult.setWorkExperience(workList);
        
        // 项目经历
        List<ThirdPartyParseResultDTO.ProjectExperience> projectList = new ArrayList<>();
        ThirdPartyParseResultDTO.ProjectExperience project = new ThirdPartyParseResultDTO.ProjectExperience();
        project.setProjectName("测试项目");
        projectList.add(project);
        parsingResult.setProjectExperience(projectList);

        // 培训经历
        List<ThirdPartyParseResultDTO.TrainingExperience> trainingList = new ArrayList<>();
        ThirdPartyParseResultDTO.TrainingExperience training = new ThirdPartyParseResultDTO.TrainingExperience();
        training.setOrganizationName("培训机构");
        training.setSubject("Java高级培训");
        trainingList.add(training);
        parsingResult.setTrainingExperience(trainingList);

        // 技能信息
        ThirdPartyParseResultDTO.Others others = new ThirdPartyParseResultDTO.Others();
        others.setItSkills(List.of("Java", "Spring"));
        parsingResult.setOthers(others);
        
        result.setParsingResult(parsingResult);
        return result;
    }
}
