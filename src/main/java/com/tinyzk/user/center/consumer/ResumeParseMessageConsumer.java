package com.tinyzk.user.center.consumer;

import com.alibaba.fastjson2.JSON;
import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.ResumeParseRecords;
import com.tinyzk.user.center.mapper.ResumeParseRecordsMapper;
import com.tinyzk.user.center.service.OSSFileStorageService;
import com.tinyzk.user.center.service.ResumeDataConversionService;
import com.tinyzk.user.center.service.ThirdPartyResumeParseService;
import com.tinyzk.user.center.service.BatchDatabaseService;
import com.tinyzk.user.center.mapper.UserProfileMapper;
import com.tinyzk.user.center.mapper.UserContactMethodsMapper;
import com.tinyzk.user.center.mapper.UserEducationHistoryMapper;
import com.tinyzk.user.center.mapper.UserWorkHistoryMapper;
import com.tinyzk.user.center.mapper.UserProjectHistoryMapper;
import com.tinyzk.user.center.mapper.UserSkillsMapper;
import com.tinyzk.user.center.mapper.UserTrainingMapper;
import com.tinyzk.user.center.entity.*;
import org.springframework.transaction.annotation.Transactional;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 简历解析消息消费者
 */
@Component
@Slf4j
@RocketMQMessageListener(
    topic = "RESUME_PARSE_TOPIC",
    consumerGroup = "resume-parse-consumer-group",
    consumeMode = org.apache.rocketmq.spring.annotation.ConsumeMode.ORDERLY, // 顺序消费
    maxReconsumeTimes = 3 // 最大重试次数
)
public class ResumeParseMessageConsumer implements RocketMQListener<ResumeParseMessage> {

    private final ThirdPartyResumeParseService parseService;
    private final OSSFileStorageService ossService;
    private final ResumeDataConversionService conversionService;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final BatchDatabaseService batchDatabaseService;
    private final UserProfileMapper userProfileMapper;
    private final UserContactMethodsMapper userContactMethodsMapper;
    private final UserEducationHistoryMapper userEducationHistoryMapper;
    private final UserWorkHistoryMapper userWorkHistoryMapper;
    private final UserProjectHistoryMapper userProjectHistoryMapper;
    private final UserSkillsMapper userSkillsMapper;
    private final UserTrainingMapper userTrainingMapper;
    private final RateLimiter consumerLimiter;
    private final CircuitBreaker apiCircuitBreaker;
    private final MeterRegistry meterRegistry;
    public ResumeParseMessageConsumer(ThirdPartyResumeParseService parseService,
                                    OSSFileStorageService ossService,
                                    ResumeDataConversionService conversionService,
                                    ResumeParseRecordsMapper parseRecordsMapper,
                                    BatchDatabaseService batchDatabaseService,
                                    UserProfileMapper userProfileMapper,
                                    UserContactMethodsMapper userContactMethodsMapper,
                                    UserEducationHistoryMapper userEducationHistoryMapper,
                                    UserWorkHistoryMapper userWorkHistoryMapper,
                                    UserProjectHistoryMapper userProjectHistoryMapper,
                                    UserSkillsMapper userSkillsMapper,
                                    UserTrainingMapper userTrainingMapper,
                                    MeterRegistry meterRegistry) {
        this.parseService = parseService;
        this.ossService = ossService;
        this.conversionService = conversionService;
        this.parseRecordsMapper = parseRecordsMapper;
        this.batchDatabaseService = batchDatabaseService;
        this.userProfileMapper = userProfileMapper;
        this.userContactMethodsMapper = userContactMethodsMapper;
        this.userEducationHistoryMapper = userEducationHistoryMapper;
        this.userWorkHistoryMapper = userWorkHistoryMapper;
        this.userProjectHistoryMapper = userProjectHistoryMapper;
        this.userSkillsMapper = userSkillsMapper;
        this.userTrainingMapper = userTrainingMapper;
        this.meterRegistry = meterRegistry;
        // 使用Resilience4j的RateLimiter
        this.consumerLimiter = RateLimiter.of("message-consumer",
            RateLimiterConfig.custom()
                .limitForPeriod(30)
                .limitRefreshPeriod(java.time.Duration.ofSeconds(1))
                .timeoutDuration(java.time.Duration.ofMillis(100))
                .build());

        // 使用Resilience4j的CircuitBreaker
        this.apiCircuitBreaker = CircuitBreaker.of("third-party-api",
            CircuitBreakerConfig.custom()
                .failureRateThreshold(60)
                .waitDurationInOpenState(java.time.Duration.ofSeconds(60))
                .slidingWindowSize(10)
                .minimumNumberOfCalls(5)
                .build());
    }

    @Override
    public void onMessage(ResumeParseMessage message) {
        Timer.Sample sample = Timer.start(meterRegistry);
        String messageId = message.getMessageId();

        try {
            log.info("开始处理简历解析消息: messageId={}, batchId={}, fileName={}",
                    messageId, message.getBatchId(), message.getFileName());

            // 使用Resilience4j限流控制
            consumerLimiter.executeRunnable(() -> {
                processResumeParseMessage(message);
            });

            meterRegistry.counter("mq.message.consume.success",
                "topic", "RESUME_PARSE_TOPIC").increment();

            log.info("简历解析消息处理完成: messageId={}", messageId);

        } catch (CallNotPermittedException e) {
            log.warn("第三方API熔断，消息将重试: messageId={}", messageId);
            meterRegistry.counter("mq.message.consume.circuit_breaker",
                "topic", "RESUME_PARSE_TOPIC").increment();
            throw new RuntimeException("API熔断，触发重试", e);

        } catch (Exception e) {
            log.error("处理简历解析消息失败: messageId={}", messageId, e);
            meterRegistry.counter("mq.message.consume.failure",
                "topic", "RESUME_PARSE_TOPIC").increment();

            // 根据错误类型决定是否重试
            if (isRetryableError(e)) {
                throw e; // 触发重试
            } else {
                // 不可重试错误，记录到死信队列
                handleNonRetryableError(message, e);
            }
        } finally {
            sample.stop(Timer.builder("mq.message.consume.duration")
                .tag("topic", "RESUME_PARSE_TOPIC")
                .register(meterRegistry));
        }
    }

    /**
     * 处理简历解析消息的核心逻辑
     */
    private void processResumeParseMessage(ResumeParseMessage message) {
        Long parseRecordId = null;
        
        try {
            // 1. 创建解析记录
            parseRecordId = createParseRecord(message);
            
            // 2. 从OSS下载文件
            byte[] fileContent = downloadFileFromOSS(message);
            
            // 3. 创建MultipartFile对象
            MultipartFile multipartFile = createMultipartFile(message, fileContent);
            
            // 4. 调用第三方API解析简历
            ThirdPartyParseResultDTO parseResult = callThirdPartyApiWithCircuitBreaker(multipartFile);
            
            // 5. 更新解析记录状态
            updateParseRecordWithResult(parseRecordId, parseResult);
            
            // 6. 转换并保存解析数据
            if (message.getUserId() != null) {
                saveConvertedData(message.getUserId(), parseResult, message);
            }
            
            log.info("简历解析处理成功: messageId={}, parseRecordId={}", 
                    message.getMessageId(), parseRecordId);
                    
        } catch (Exception e) {
            // 更新解析记录为失败状态
            if (parseRecordId != null) {
                updateParseRecordWithError(parseRecordId, e);
            }
            throw e;
        }
    }

    /**
     * 创建解析记录
     */
    private Long createParseRecord(ResumeParseMessage message) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setUserId(message.getUserId());
        record.setOriginalFilename(message.getFileName());
        record.setFileSize(message.getFileSize());
        record.setFileType(message.getFileType());
        record.setParseStatus(1); // 解析中
        record.setCreatedAt(LocalDateTime.now());
        
        parseRecordsMapper.insert(record);
        return record.getRecordId();
    }

    /**
     * 从OSS下载文件
     */
    private byte[] downloadFileFromOSS(ResumeParseMessage message) {
        try {
            return ossService.downloadFile(message.getOssKey());
        } catch (Exception e) {
            log.error("从OSS下载文件失败: ossKey={}", message.getOssKey(), e);
            throw new RuntimeException("文件下载失败", e);
        }
    }

    /**
     * 创建MultipartFile对象
     */
    private MultipartFile createMultipartFile(ResumeParseMessage message, byte[] content) {
        return new MultipartFile() {
            @Override
            public String getName() { return "file"; }

            @Override
            public String getOriginalFilename() { return message.getFileName(); }

            @Override
            public String getContentType() { return ResumeParseMessageConsumer.this.getContentType(message.getFileType()); }

            @Override
            public boolean isEmpty() { return content.length == 0; }

            @Override
            public long getSize() { return content.length; }

            @Override
            public byte[] getBytes() { return content; }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(content);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException {
                throw new UnsupportedOperationException("transferTo not supported");
            }
        };
    }

    /**
     * 使用熔断器保护的第三方API调用
     */
    private ThirdPartyParseResultDTO callThirdPartyApiWithCircuitBreaker(MultipartFile file) {
        return apiCircuitBreaker.executeSupplier(() -> {
            return parseService.parseResumeWithRetry(file);
        });
    }

    /**
     * 更新解析记录（成功）
     */
    private void updateParseRecordWithResult(Long recordId, ThirdPartyParseResultDTO result) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(result.getErrorCode() == 0 ? 2 : 3); // 2-成功, 3-失败
            record.setParseResult(JSON.toJSONString(result));
            record.setThirdPartyId(result.getCvId());
            record.setErrorMessage(result.getErrorMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败: recordId={}", recordId, e);
        }
    }

    /**
     * 更新解析记录（失败）
     */
    private void updateParseRecordWithError(Long recordId, Exception error) {
        try {
            ResumeParseRecords record = new ResumeParseRecords();
            record.setRecordId(recordId);
            record.setParseStatus(3); // 失败
            record.setErrorMessage(error.getMessage());
            record.setUpdatedAt(LocalDateTime.now());
            
            parseRecordsMapper.updateById(record);
        } catch (Exception e) {
            log.error("更新解析记录失败状态失败: recordId={}", recordId, e);
        }
    }

    /**
     * 保存转换后的数据
     */
    @Transactional(rollbackFor = Exception.class)
    private void saveConvertedData(Long userId, ThirdPartyParseResultDTO parseResult, ResumeParseMessage message) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            log.info("开始转换和保存简历数据: userId={}, messageId={}", userId, message.getMessageId());

            // 检查解析结果是否有效
            if (parseResult.getParsingResult() == null) {
                log.warn("解析结果为空，跳过数据转换: userId={}", userId);
                return;
            }

            var parsingResult = parseResult.getParsingResult();
            int totalSavedRecords = 0;

            // 1. 转换并保存基本信息
            if (parsingResult.getBasicInfo() != null) {
                totalSavedRecords += saveUserProfile(userId, parsingResult.getBasicInfo());
            }

            // 2. 转换并批量保存联系方式
            if (parsingResult.getContactInfo() != null) {
                totalSavedRecords += saveContactMethods(userId, parsingResult.getContactInfo());
            }

            // 3. 转换并批量保存教育经历
            if (parsingResult.getEducationExperience() != null && !parsingResult.getEducationExperience().isEmpty()) {
                totalSavedRecords += saveEducationHistory(userId, parsingResult.getEducationExperience());
            }

            // 4. 转换并批量保存工作经历
            if (parsingResult.getWorkExperience() != null && !parsingResult.getWorkExperience().isEmpty()) {
                totalSavedRecords += saveWorkHistory(userId, parsingResult.getWorkExperience());
            }

            // 5. 转换并批量保存项目经历
            if (parsingResult.getProjectExperience() != null && !parsingResult.getProjectExperience().isEmpty()) {
                totalSavedRecords += saveProjectHistory(userId, parsingResult.getProjectExperience());
            }

            // 6. 转换并批量保存培训经历
            if (parsingResult.getTrainingExperience() != null && !parsingResult.getTrainingExperience().isEmpty()) {
                totalSavedRecords += saveTrainingHistory(userId, parsingResult.getTrainingExperience());
            }

            // 7. 转换并批量保存技能信息
            if (parsingResult.getOthers() != null) {
                totalSavedRecords += saveSkills(userId, parsingResult.getOthers());
            }

            log.info("数据转换和保存完成: userId={}, messageId={}, 总保存记录数={}",
                    userId, message.getMessageId(), totalSavedRecords);

            // 记录成功指标
            meterRegistry.counter("resume.data.conversion.success").increment();
            meterRegistry.counter("resume.data.records.saved", "user_id", userId.toString())
                    .increment(totalSavedRecords);

        } catch (Exception e) {
            log.error("数据转换和保存失败: userId={}, messageId={}", userId, message.getMessageId(), e);
            meterRegistry.counter("resume.data.conversion.failure").increment();
            meterRegistry.counter("resume.data.conversion.error",
                    "error_type", e.getClass().getSimpleName()).increment();

            // 重新抛出异常以触发事务回滚
            throw new RuntimeException("简历数据保存失败: " + e.getMessage(), e);
        } finally {
            sample.stop(Timer.builder("resume.data.conversion.duration")
                    .tag("user_id", userId.toString())
                    .register(meterRegistry));
        }
    }

    /**
     * 保存用户基本信息
     */
    private int saveUserProfile(Long userId, ThirdPartyParseResultDTO.BasicInfo basicInfo) {
        try {
            UserProfile userProfile = conversionService.convertBasicInfo(userId, basicInfo);
            if (userProfile == null) {
                log.debug("基本信息转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 检查是否已存在用户资料
            UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
            int savedCount = 0;

            if (existingProfile != null) {
                // 更新现有资料，只更新非空字段
                if (userProfile.getNickname() != null) {
                    existingProfile.setNickname(userProfile.getNickname());
                }
                if (userProfile.getGender() != null) {
                    existingProfile.setGender(userProfile.getGender());
                }
                if (userProfile.getBirthday() != null) {
                    existingProfile.setBirthday(userProfile.getBirthday());
                }
                if (userProfile.getRegionName() != null) {
                    existingProfile.setRegionName(userProfile.getRegionName());
                }
                if (userProfile.getAddress() != null) {
                    existingProfile.setAddress(userProfile.getAddress());
                }
                if (userProfile.getBio() != null) {
                    existingProfile.setBio(userProfile.getBio());
                }

                savedCount = userProfileMapper.updateById(existingProfile);
                log.debug("更新用户基本信息: userId={}, 更新记录数={}", userId, savedCount);
            } else {
                // 创建新的用户资料
                savedCount = userProfileMapper.insert(userProfile);
                log.debug("创建用户基本信息: userId={}, 插入记录数={}", userId, savedCount);
            }

            meterRegistry.counter("resume.data.user_profile.saved").increment();
            return savedCount;

        } catch (Exception e) {
            log.error("保存用户基本信息失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.user_profile.error").increment();
            throw new RuntimeException("保存用户基本信息失败", e);
        }
    }

    /**
     * 批量保存联系方式
     */
    private int saveContactMethods(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        try {
            List<UserContactMethods> contactMethods = conversionService.convertContactInfo(userId, contactInfo);
            if (contactMethods.isEmpty()) {
                log.debug("联系方式转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的联系方式，避免重复
            List<UserContactMethods> newContacts = new ArrayList<>();
            for (UserContactMethods contact : contactMethods) {
                UserContactMethods existing = userContactMethodsMapper.selectByUserIdAndTypeAndValue(
                        userId, contact.getContactType(), contact.getContactValue());
                if (existing == null) {
                    newContacts.add(contact);
                } else {
                    log.debug("联系方式已存在，跳过: userId={}, type={}, value={}",
                            userId, contact.getContactType(), contact.getContactValue());
                }
            }

            if (newContacts.isEmpty()) {
                log.debug("没有新的联系方式需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newContacts, userContactMethodsMapper).get();
            log.debug("批量保存联系方式: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.contact_methods.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存联系方式失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.contact_methods.error").increment();
            throw new RuntimeException("批量保存联系方式失败", e);
        }
    }

    /**
     * 批量保存教育经历
     */
    private int saveEducationHistory(Long userId, List<ThirdPartyParseResultDTO.EducationExperience> educationList) {
        try {
            List<UserEducationHistory> educationHistory = conversionService.convertEducationExperience(userId, educationList);
            if (educationHistory.isEmpty()) {
                log.debug("教育经历转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的教育经历，避免重复
            List<UserEducationHistory> newEducations = new ArrayList<>();
            for (UserEducationHistory education : educationHistory) {
                UserEducationHistory existing = userEducationHistoryMapper.selectByUserIdAndSchoolAndMajor(
                        userId, education.getSchoolName(), education.getMajor());
                if (existing == null) {
                    newEducations.add(education);
                } else {
                    log.debug("教育经历已存在，跳过: userId={}, school={}, major={}",
                            userId, education.getSchoolName(), education.getMajor());
                }
            }

            if (newEducations.isEmpty()) {
                log.debug("没有新的教育经历需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newEducations, userEducationHistoryMapper).get();
            log.debug("批量保存教育经历: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.education_history.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存教育经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.education_history.error").increment();
            throw new RuntimeException("批量保存教育经历失败", e);
        }
    }

    /**
     * 批量保存工作经历
     */
    private int saveWorkHistory(Long userId, List<ThirdPartyParseResultDTO.WorkExperience> workList) {
        try {
            List<UserWorkHistory> workHistory = conversionService.convertWorkExperience(userId, workList);
            if (workHistory.isEmpty()) {
                log.debug("工作经历转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的工作经历，避免重复
            List<UserWorkHistory> newWorkHistory = new ArrayList<>();
            for (UserWorkHistory work : workHistory) {
                UserWorkHistory existing = userWorkHistoryMapper.selectByUserIdAndCompanyAndPosition(
                        userId, work.getCompanyName(), work.getPositionName());
                if (existing == null) {
                    newWorkHistory.add(work);
                } else {
                    log.debug("工作经历已存在，跳过: userId={}, company={}, position={}",
                            userId, work.getCompanyName(), work.getPositionName());
                }
            }

            if (newWorkHistory.isEmpty()) {
                log.debug("没有新的工作经历需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newWorkHistory, userWorkHistoryMapper).get();
            log.debug("批量保存工作经历: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.work_history.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存工作经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.work_history.error").increment();
            throw new RuntimeException("批量保存工作经历失败", e);
        }
    }

    /**
     * 批量保存项目经历
     */
    private int saveProjectHistory(Long userId, List<ThirdPartyParseResultDTO.ProjectExperience> projectList) {
        try {
            List<UserProjectHistory> projectHistory = conversionService.convertProjectExperience(userId, projectList);
            if (projectHistory.isEmpty()) {
                log.debug("项目经历转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的项目经历，避免重复
            List<UserProjectHistory> newProjects = new ArrayList<>();
            for (UserProjectHistory project : projectHistory) {
                UserProjectHistory existing = userProjectHistoryMapper.selectByUserIdAndProjectName(
                        userId, project.getProjectName());
                if (existing == null) {
                    newProjects.add(project);
                } else {
                    log.debug("项目经历已存在，跳过: userId={}, project={}",
                            userId, project.getProjectName());
                }
            }

            if (newProjects.isEmpty()) {
                log.debug("没有新的项目经历需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newProjects, userProjectHistoryMapper).get();
            log.debug("批量保存项目经历: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.project_history.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存项目经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.project_history.error").increment();
            throw new RuntimeException("批量保存项目经历失败", e);
        }
    }

    /**
     * 批量保存技能信息
     */
    private int saveSkills(Long userId, ThirdPartyParseResultDTO.Others others) {
        try {
            List<UserSkills> skills = conversionService.convertSkills(userId, others);
            if (skills.isEmpty()) {
                log.debug("技能信息转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的技能，避免重复
            List<UserSkills> newSkills = new ArrayList<>();
            for (UserSkills skill : skills) {
                UserSkills existing = userSkillsMapper.selectByUserIdAndSkillName(
                        userId, skill.getSkillName());
                if (existing == null) {
                    newSkills.add(skill);
                } else {
                    log.debug("技能已存在，跳过: userId={}, skill={}",
                            userId, skill.getSkillName());
                }
            }

            if (newSkills.isEmpty()) {
                log.debug("没有新的技能需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newSkills, userSkillsMapper).get();
            log.debug("批量保存技能信息: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.skills.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存技能信息失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.skills.error").increment();
            throw new RuntimeException("批量保存技能信息失败", e);
        }
    }

    /**
     * 批量保存培训经历
     */
    private int saveTrainingHistory(Long userId, List<ThirdPartyParseResultDTO.TrainingExperience> trainingList) {
        try {
            List<UserTraining> trainingHistory = conversionService.convertTrainingExperience(userId, trainingList);
            if (trainingHistory.isEmpty()) {
                log.debug("培训经历转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            // 过滤掉已存在的培训经历，避免重复
            List<UserTraining> newTrainings = new ArrayList<>();
            for (UserTraining training : trainingHistory) {
                UserTraining existing = userTrainingMapper.selectByUserIdAndNameAndProvider(
                        userId, training.getTrainingName(), training.getTrainingProvider());
                if (existing == null) {
                    newTrainings.add(training);
                } else {
                    log.debug("培训经历已存在，跳过: userId={}, training={}, provider={}",
                            userId, training.getTrainingName(), training.getTrainingProvider());
                }
            }

            if (newTrainings.isEmpty()) {
                log.debug("没有新的培训经历需要保存: userId={}", userId);
                return 0;
            }

            // 使用批量数据库服务保存
            int savedCount = batchDatabaseService.batchInsert(newTrainings, userTrainingMapper).get();
            log.debug("批量保存培训经历: userId={}, 保存数量={}", userId, savedCount);

            meterRegistry.counter("resume.data.training_history.saved").increment(savedCount);
            return savedCount;

        } catch (Exception e) {
            log.error("批量保存培训经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.training_history.error").increment();
            throw new RuntimeException("批量保存培训经历失败", e);
        }
    }

    /**
     * 判断是否为可重试错误
     */
    private boolean isRetryableError(Exception e) {
        return e instanceof SocketTimeoutException ||
               e instanceof ConnectException ||
               e instanceof org.springframework.web.client.HttpServerErrorException ||
               (e instanceof RuntimeException && e.getMessage().contains("API熔断"));
    }

    /**
     * 处理不可重试错误
     */
    private void handleNonRetryableError(ResumeParseMessage message, Exception e) {
        try {
            log.error("不可重试错误，记录到死信队列: messageId={}, error={}",
                    message.getMessageId(), e.getMessage());

            // 记录死信消息到数据库或其他存储
            // 这里可以扩展为发送到死信队列主题
            meterRegistry.counter("mq.message.dead_letter",
                "topic", "RESUME_PARSE_TOPIC").increment();

        } catch (Exception ex) {
            log.error("处理死信消息失败: messageId={}", message.getMessageId(), ex);
        }
    }

    /**
     * 根据文件类型获取Content-Type
     */
    private String getContentType(String fileType) {
        if (fileType == null) {
            return "application/octet-stream";
        }

        switch (fileType.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }
}
