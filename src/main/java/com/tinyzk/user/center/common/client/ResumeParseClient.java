package com.tinyzk.user.center.common.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONObject;

import io.swagger.v3.oas.annotations.parameters.RequestBody;

/**
 * 小析智能-简历解析
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@FeignClient(
    name = "resume-parse-client",
    url = "${resume.parse.api-url}"
)
public interface ResumeParseClient {

    /**
     * 简历解析-文件
     */
    @PostMapping("/parse_file?rawtext=1&handle_image=1&avatar=1&parse_mode=fast&ocr_mode=accurate&ocr_service=OCR")
    String parseFile(@RequestBody MultipartFile file);


    /**
     * 简历解析-base64
     */
    @PostMapping("/parse_base?rawtext=1&handle_image=1&avatar=1&parse_mode=fast&ocr_mode=accurate&ocr_service=OCR")
    String parseBase(@RequestBody JSONObject jsonObject);


    /**
     * 简历解析-人像分析(文件)
     */
    @PostMapping("/analyze_base")
    String analyzeFile(@RequestBody MultipartFile file);


    /**
     * 简历解析-人像分析(解析后简历)
     */
    @PostMapping("/analyze_json")
    String analyzeBase(@RequestBody JSONObject jsonObject);
}
